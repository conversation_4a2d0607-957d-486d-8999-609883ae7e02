function [yfit,ampl,pos,width,integral] = curvefitZspect(x,y,n,mode,initials,ymax)

% nonlinear curve fitting program for n peaks
% inputs:    x = array of xpoints
%            y = array of spectral points
%            n = number of peaks expected
%            mode 1= gaussian, 2 = lorentzian

% x=xarr; 
% y=finspec;
% n=npeaks;
np = size(x,2);
if (size(y,2) ~= np)
    error('x and y array size mismatch')
end

if (nargin < 4)
    mode = 1;
end

if length(initials)==1
% Use n peaks - work in points units
cg = zeros(n,1);
wg = cg;
ag = cg;

cfh = figure;
plot(x,y); axis('tight');
hold on;

% First get initial estimates of position, amplitude and widths from user
% interactively.
for k = 1:n
    str1 = sprintf('Place cursor on top of peak %2i and click left button',k);
    disp(str1);
    [xk,yk,but] = ginput(1);
    cg(k) = xk;
    ag(k) = yk;
    plot(cg(k),ag(k),'k*');
    str1 = sprintf('Place cursor on leftside midpoint of peak %2i and click left button',k);
    wg(k) = 0;
    while (wg(k) == 0)
        disp(str1);
        [xk,yk,but] = ginput(1);
        wg(k) = abs((xk)-cg(k));
    end

    plot(cg(k)-wg(k),yk,'k*');
    plot(cg(k)+wg(k),yk,'k*');
end
else
    ag = initials(1,:);  % amplitude
    cg = initials(2,:);  % chemical shift 
    wg = initials(3,:);  % width
   
end


if length(initials)==1
cgmax = cg+wg;
cgmin = cg-wg;
wg = 2*wg;

amin = 0; %% confine loosely
amax = 10*ag;

pars( 1:n ) = ag;
lb(1:n) = amin;
ub(1:n) = amax;

pars( n + (1:n) ) = cg;
lb( n + (1:n) ) = cgmin;
ub( n + (1:n) ) = cgmax;


wgmin = 0.5*wg;
wgmax = 2*wg;

pars( n + n  + (1:n) ) = wg;
lb( n + n + (1:n) ) = wgmin;
ub( n + n + (1:n) ) = wgmax;
else   %% when we use the fitting parameters from Model spectrum as our initial values
    
pars( 1:n ) = ag;  %% this is amplitude
lb(1:n) = 0.01*ag;
ub(1:n) = 10*ag;

pars( n + (1:n) ) = cg;  %% frequency offset in ppm
lb( n + (1:n) ) = cg-0.1;
ub( n + (1:n) ) = cg+0.1;
% lb( n + (2) ) = cg-1; %% for MTR peak only, kc 06-19-2012
% ub( n + (2) ) = cg+1;
% lb( n + (3) ) = cg-0.2*wg; %% for H2O peak only, kc 06-19-2012
% ub( n + (3) ) = cg+0.2*wg;

pars( n + n  + (1:n) ) = wg;  %% linewidth of the peak
lb( n + n + (1:n) ) = 0.5*wg;
ub( n + n + (1:n) ) = 2*wg; 

    
end

  options1=optimset('MaxFunEvals',10000,'MaxIter', 10000, 'TolFun',1e-5,'TolX',1e-4,'Display','off');


if (mode == 1)

    %ip=nlinfit(x,y,@composite,pars);
    ip = lsqcurvefit(@composite,pars,x,y,lb,ub,options1);
    ampl = abs(ip(1:n));
    pos = ip( (1:n) + n);
    width = abs(ip( (1:n) + n + n));
    yfit = x*0;
    for k = 1:n
        a = abs( ip(k) );
        p = ip(k+n);
        w = abs( ip(k+n+n) );
        xpw = (x-p)/w;
        yfit = yfit + a * exp( -( (xpw/0.6006).^2 ) ); % 0.6006 = 0.5/(sqrt(ln(2.0)))
        integral(k) = sum( a * exp( -( (xpw/0.6006).^2 ) ) );
    end

else
    %ip=nlinfit(x,y,@compositel,pars);
    ip = lsqcurvefit(@compositel,pars,x,y,lb,ub,options1);

    ampl = abs(ip(1:n));
    pos = ip( (1:n) + n);
    width = abs(ip( (1:n) + n + n));
    yfit = x*0;
    for k = 1:n
        a = abs( ip(k) );
        p = ip(k+n);
        w = abs( ip(k+n+n) );
        xpw = (x-p)/w;
        yfit = yfit + a ./ ( (4.0*(xpw .*xpw)) + 1.0 );
        integral(k) = sum( a ./ ( (4.0*(xpw .*xpw)) + 1.0 ) );
    end

end

plot(x,yfit,'r');axis('tight');
axis([min(x),max(x),-0.1*ymax, ymax]);

%close(cfh);

return

function yfit = composite(ip,x)

n = (size(ip,2))/3;
yfit = x*0 ;
for k = 1:n
    a = abs( ip(k) );
    p = ip(k+n);
    w = abs( ip(k+n+n) );
    xpw = (x-p)/w;
    yfit = yfit + a * exp( -( (xpw/0.6006).^2 ) ); % 0.6006 = 0.5/(sqrt(ln(2.0)))
end


function yfit = compositel(ip,x)

n = (size(ip,2))/3;
yfit = x*0 ;
for k = 1:n
    a = abs( ip(k) );
    p = ip(k+n);
    w = abs( ip(k+n+n) );
    xpw = (x-p)/w;
    yfit = yfit + a ./ ( (4.0*(xpw .*xpw)) + 1.0 );
end




