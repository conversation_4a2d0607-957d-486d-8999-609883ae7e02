clc; clear; % 清空命令行窗口并清除所有变量

fprintf('=== 步骤0：5池洛伦兹拟合脚本 - 真实NII数据拟合 ===\n');
fprintf('=== Step 0: Five-Pool Lorentzian Fitting - Real NII Data Fitting ===\n\n');

% 项目根目录配置
project_root = pwd;
fprintf('   项目根目录: %s\n', project_root);

% 数据文件夹路径（在训练模型文件夹内）
DataDir = 'Data'; % 直接指向Data文件夹，适应现有数据格式
if ~exist(DataDir, 'dir')
    mkdir(DataDir);
    fprintf('   ✅ 已创建CEST输入数据文件夹: %s\n', DataDir);
end

FreqNum = '48'; % 设置频率点数

% 工具箱根目录（当前文件夹下的CestToolBox）
GuiRootPath = fullfile(project_root, 'CestToolBox'); % 工具箱路径
if ~exist(GuiRootPath, 'dir')
    error('❌ 未找到CestToolBox工具箱！请确保工具箱已复制到训练模型文件夹下');
end
addpath(genpath(GuiRootPath)); % 添加工具箱路径
fprintf('   ✅ 已添加CestToolBox工具箱路径: %s\n', GuiRootPath);

FreqPath = GuiRootPath; % 频率文件路径等于工具箱路径
FreqName = ls(fullfile(FreqPath, ['*', FreqNum, 'points*.txt'])); % 查找对应频率点数的频率文件名
if isempty(FreqName)
    error('❌ 未找到频率文件！请确保CestToolBox中有对应的频率文件');
end
FreqList = importdata(fullfile(FreqPath, FreqName)); % 导入频率列表
fprintf('   ✅ 已加载频率文件: %s\n', FreqName);

% 序列名称设置
SeqList = {'APT_CEST_15'}; % 设置要处理的序列名称
ProcessingModel = 2; % 选择处理模式（1为自动阈值分割，2为手动ROI）

% 拟合结果输出路径（与Data同级）
output_base = fullfile('A4CestParam5Pools_48points_APT_CEST_15');  % 保持原来的输出命名
if ~exist(output_base, 'dir')
    mkdir(output_base);
    fprintf('   ✅ 已创建拟合结果输出文件夹: %s\n', output_base);
end

fprintf('\n>> 开始执行5池洛伦兹拟合\n');

for i = 1:size(SeqList, 2)
    SeqName = SeqList{1, i}; % 获取当前序列名称
    fprintf('   正在处理序列: %s\n', SeqName);

    % 调用主处理函数
    IPL_ParameterCalculations_CEST_5Lorenitz_AllPointsLessThanSix(DataDir, FreqList, ProcessingModel, SeqName, output_base);
end

fprintf('   ✅ 5池洛伦兹拟合完成\n');


function IPL_ParameterCalculations_CEST_5Lorenitz_AllPointsLessThanSix(DataDir, FreqList, ProcessingModel, SeqName, output_base)
% 主处理函数，进行五池Lorentzian拟合

%% step one: constants
% fitting frequency
ppm0=FreqList'/128; % 计算ppm化学位移
%ȷƵʷΧ
fittingrange=find(abs(ppm0)<=6); % 找到±6ppm范围内的索引
% StartPoint=a(1,end)+1;
% fittingrange=[StartPoint:size(FreqList',2)];
% fitting initial values
initials=[10,20,70,12,10;...  % 五个池的初始幅值（NOE、MT、DSW、Cr、APT）
    -3.6,-1.5,0.001,2.04,3.5;...% 五个池的初始位置
    4.0,20,2.5,2.23,5]; % 五个池的初始宽度
mode = 2; ymax=100; % 拟合模式和最大y值
%% apply to pixel by pixel
% ppm0=[5000,5000,5000,600,575,550,525,500,475,450,425,400,375,350,325,300,...
%     275,250,225,200,175,150,125,100,75,50,25,0,-25,-50,-75,-100,...
%     -125,-150,-175,-200,-225,-250,-275,-300,-325,-350,-375,-400,-425,-450,-475,-500,...
%     -525,-550,-575,-600]/128;

ppm = [];ppm = ppm0(1:length(ppm0));xarr0=rot90(rot90(ppm(fittingrange))); % 取拟合范围内的ppm
%% step two: data reading
% 使用传入的output_base参数作为输出路径
ParamPath = output_base; % 使用传入的输出路径
if ~exist(ParamPath, 'dir')
    mkdir(ParamPath); % 创建参数输出文件夹
end
FileName=dir(DataDir);FileName(1:2,:)=''; % 获取数据目录下的所有文件夹，去除'.'和'..'
for i=1:length(FileName)
    FileName2=dir(fullfile(DataDir,FileName(i).name));;FileName2(1:2,:)=''; % 获取每个子文件夹下的文件，去除'.'和'..'
    for j=1:length(FileName2)
        if ~exist(fullfile(ParamPath,FileName(i).name,FileName2(j).name,'Rsquare.dcm'),'file') % 如果还没有处理过该数据
            disp(['Processing data in ',FileName(i).name,'  ',FileName2(j).name]); % 显示正在处理的信息
            if exist(fullfile(DataDir,FileName(i).name,FileName2(j).name,[SeqName,'.nii']),'file') % 如果存在对应的nii文件
                disp(['开始处理nii文件: ', fullfile(DataDir,FileName(i).name,FileName2(j).name,[SeqName,'.nii'])]);
                [data,head]=rest_ReadNiftiImage(fullfile(DataDir,FileName(i).name,FileName2(j).name,[SeqName,'.nii'])); % 读取nii图像
                FittingMaskTemp=data(:,:,1);MaskThreshold=prctile(FittingMaskTemp(:),10); % 取第一层图像，计算10%分位数阈值
                FittingMaskTemp(FittingMaskTemp<MaskThreshold)=0;FittingMaskTemp(FittingMaskTemp~=0)=1; % 低于阈值的设为0，其余为1
                FittingROI= FittingMaskTemp; % 得到初步掩膜
                %             FittingROI=imread(fullfile(DataDir,FileName(i).name,FileName2(j).name,'FittingROI.tif'));
                FittingROI=double( FittingROI); FittingROI( FittingROI>0)=1; % 转为double型，非零为1
                if size(data,3)==size(FreqList',2) % 检查数据层数是否与频率点数一致
                    %         W = Imginfo.Columns;H = Imginfo.Rows;Img0 = zeros(W,H,length(filename));
                    W = size(data,2);H = size(data,1);Img0 = zeros(W,H,size(data,3)); % 初始化图像矩阵
                    for n = 1:size(data,3)
                        Img0(:,:,n) = data(:,:,n); % 逐层赋值
                    end
                    Img =Img0; % 得到完整三维图像
                    %% step 3: ROI drawing
                    allimages=Img;ImageROIs=[];ImageROIs=allimages(:,:,max(fittingrange):-1:min(fittingrange)); % 取拟合范围内的图像层
                    ImageROIs1=[];ImageROIs1=ImageROIs;pixelsize=[size(ImageROIs)];ImageROIs2=[]; % 初始化变量
                    for ii=1:length(fittingrange)
                        tempimg=imfilter(squeeze(ImageROIs1(:,:,ii)), ones(1,1)/1); % 对每层图像滤波
                        tempimg2=imresize(tempimg,[pixelsize(1,1),pixelsize(1,2)]); % 重采样到原始大小
                        ImageROIs2(:,:,ii)=tempimg2; % 存储处理后的图像
                    end
                    ImageForMask=ImageROIs2(:,:,1); % 取第一层用于掩膜
                    if ProcessingModel==1
                        ImageForMask(ImageForMask<prctile(ImageForMask(:),20))=0;ImageForMask(ImageForMask~=0)=1; % 自动阈值分割生成掩膜
                        mask44=ImageForMask;
                        %                     mask44=ImageForMask.*double(FittingROI);
                        npeaks=5; % 设置拟合峰数为5
                    elseif ProcessingModel==2
                        figure;imshow(ImageForMask,[]); % 显示图像，手动画ROI
                        BW = roipoly;
                        mask44=BW;npeaks=5; % 手动掩膜，拟合峰数为5
                    else
                        disp(['No selected processing patterns']); % 未选择处理模式
                        break;
                    end
                    ampl1=zeros(pixelsize(1,1),pixelsize(1,2),npeaks);pos1=zeros(pixelsize(1,1),pixelsize(1,2),npeaks);width1=zeros(pixelsize(1,1),pixelsize(1,2),npeaks);integral1=zeros(pixelsize(1,1),pixelsize(1,2),npeaks); % 初始化参数矩阵
                    Rsquare1=zeros(pixelsize(1,1),pixelsize(1,2));ppm100=imresize(squeeze(allimages(:,:,1)),[pixelsize(1,1),pixelsize(1,2)]); % 初始化R方和归一化因子
                    %% step 4: fitting process
                    tic;xstepsize=0.1;xinteg=-100:xstepsize:100; % 计时，设置积分步长和区间
                    if ProcessingModel==2
                        BW =mask44;
                        for iii=1:size(ImageROIs2,3)
                            tempSig=ImageROIs2(:,:,iii);
                            imshow(ImageROIs2(:,:,iii),[]);
                            NN(iii,1)=mean(tempSig(BW==1)); % 计算ROI内每层的均值
                        end
                        
                        for nn = 1:size(Img0,3)
                            tempSig11=Img0(:,:,nn);
                            NNN(nn,1)=mean(tempSig11(BW==1)); % 计算原始图像ROI内每层的均值
                        end
                        %         plot(1:1:52,NNN)
                        invivoZ0=NN;PPM100=mean(ppm100(BW==1));  invivoZ=smooth(invivoZ0,1); %%% do not use any smoothness!!!!!!!!
                        invivoZ=100*invivoZ./PPM100; % 归一化
                        % 去掉RawData图：figure; plot(xarr0,invivoZ0);title('RawData'); % 绘制原始数据
                        B0mapOffset= xarr0(find(NN==min(NN))); % 计算B0漂移
                        if length(B0mapOffset)>1  % 修复：检查长度而不是列数
                            B0mapOffset=B0mapOffset(1); % 修复：取第一个元素而不是(1,2)
                        end
                        xarr=xarr0-B0mapOffset; % 校正ppm
                        xarr1=xarr;
                        finspec=100-invivoZ'; % 计算Z谱
                        % 去掉offset图：figure; plot(xarr,finspec);title(['offset=',num2str(B0mapOffset)]) % 绘制校正后Z谱
                        finspec1=spline(xarr,finspec,xarr1); % 插值
                        
                        [specfit1,ampl10,pos10,width10,integral10] = curvefitZspect2ppmForPlot(xarr1,finspec1,npeaks,mode,initials,ymax); % 五池Lorentzian拟合
                        % ====== 合并的五池拟合曲线图（中英文结合） ======
                        figure;
                        hold on;
                        plot(xarr1, finspec1, 'bo', 'DisplayName', '原始Z谱数据');
                        plot(xarr1, specfit1, 'r-', 'LineWidth', 2, 'DisplayName', '总拟合曲线');

                        % 绘制五个池的单独拟合曲线（中英文结合）
                        colors = {'g','m','c','k','y'};
                        pool_names = {'NOE池', 'MT池', 'Water池', 'Cr池', 'APT池'};
                        pool_abbr = {'NOE', 'MT', 'Water', 'Cr', 'APT'};
                        for k = 1:npeaks
                            a = ampl10(k);
                            p = pos10(k);
                            w = width10(k);
                            xpw = (xarr1-p)/w;
                            lorentz = a ./ ( (4.0*(xpw .*xpw)) + 1.0 );
                            plot(xarr1, lorentz, colors{k}, 'LineWidth', 1.5, 'DisplayName', [pool_names{k}, ' (', pool_abbr{k}, ')']);
                        end

                        legend show;
                        xlabel('化学位移 (ppm)');
                        ylabel('Z谱信号强度');
                        title(['五池Lorentz拟合曲线 (B0 offset=', num2str(B0mapOffset), ' ppm)']);
                        grid on;
                        hold off;
                        
                    elseif ProcessingModel==1
                        for xx=1:pixelsize(1,1)
                            for yy=1:pixelsize(1,2)
                                %          for xx=28:pixelsize
                                %      for yy=28:pixelsize
                                if mask44(xx,yy)==1 && ppm100(xx,yy)~=0
                                    %             disp(num2str(xx));
                                    invivoZ0= squeeze(ImageROIs2(xx,yy,:)); invivoZ=smooth(invivoZ0,1); %%% do not use any smoothness!!!!!!!!
                                    invivoZ=100*invivoZ./ppm100(xx,yy); % 归一化
                                    %           figure; plot(ppm-B0map11(xx,yy), invivoZ,'.');
                                    B0mapOffset= xarr0(find(ImageROIs2(xx, yy, :)==min(ImageROIs2(xx, yy, :)))); % 计算B0漂移
                                    if length(B0mapOffset)>1  % 修复：检查长度而不是列数
                                        B0mapOffset=B0mapOffset(1); % 修复：取第一个元素而不是(1,2)
                                    end
                                    xarr=xarr0-B0mapOffset; % 校正ppm
                                    xarr1=xarr;
                                    finspec=100-invivoZ'; % 计算Z谱
                                    finspec1=spline(xarr,finspec,xarr1); % 插值
                                    [specfit1,ampl10,pos10,width10,integral10] = curvefitZspect2ppm(xarr1,finspec1,npeaks,mode,initials,ymax); % 五池Lorentzian拟合
                                    disp(['finspec1: ', num2str(finspec1(:)')]);
                                    disp(['ampl10: ', num2str(ampl10(:)')]);
                                    for nn=1:npeaks
                                        xpw = (xinteg-pos10(nn))./width10(nn);
                                        integral10(nn) = xstepsize*sum( ampl10(nn)./((4.0*(xpw .*xpw)) + 1.0 )); % 计算每个峰的积分
                                    end
                                    goodnessfit1=corrcoef(specfit1, finspec1); % 计算拟合优度
                                    ampl1(xx,yy,1:npeaks) = ampl10; % 存储幅值
                                    pos1(xx,yy,1:npeaks) = pos10; % 存储位置
                                    width1(xx,yy,1:npeaks) = width10; % 存储宽度
                                    integral1(xx,yy,1:npeaks) = integral10; % 存储积分
                                    Rsquare1 (xx,yy)=goodnessfit1(1,2).^2; % 存储R方
                                    %%
                                    indspec = zeros(length(xarr1),npeaks);
                                    for ip = 1:npeaks
                                        for ix = 1:length(xarr1)
                                            xpw = (xarr1(ix)-pos10(ip))/width10(ip);
                                            indspec(ix-1+1,ip) = ampl10(ip) * ( 1 ./ ( 1.0 + (4 * xpw * xpw) ) ); % 计算每个峰的拟合曲线
                                        end
                                        %plot(xarr1,indspec(:,ip),'k');
                                    end
                                    %axis('tight');
                                end
                            end
                        end
                        toc
                        %                     close all
                        disp('五池Lorentzian拟合完成，开始保存DICOM参数图像...');
                        %% step five: image show
                        SavePath=fullfile(ParamPath,FileName(i).name,FileName2(j).name);mkdir(SavePath); % 创建保存路径
                        cd(SavePath)
                        dicomwrite(int16(Rsquare1),['Rsquare.dcm'],'grayscale','63355'); % 保存R方图像
                        % 保存五池幅值参数
                        dicomwrite(int16(ampl1(:,:,3)),['WaterAmplitude.dcm'],'grayscale','63355'); % 保存水峰幅值图像
                        dicomwrite(int16(ampl1(:,:,2)),['MTRAmplitude.dcm'],'grayscale','63355');   % 保存MTR幅值图像
                        dicomwrite(int16(ampl1(:,:,5)),['APTAmplitude.dcm'],'grayscale','63355');   % 保存APT幅值图像
                        dicomwrite(int16(ampl1(:,:,1)),['NOEAmplitude.dcm'],'grayscale','63355');   % 保存NOE幅值图像
                        dicomwrite(int16(ampl1(:,:,4)),['CrAmplitude.dcm'],'grayscale','63355');    % 保存Cr幅值图像

                        % 保存五池积分参数
                        dicomwrite(int16(integral1(:,:,3)),['WaterIntegral.dcm'],'grayscale','63355'); % 保存水峰积分图像
                        dicomwrite(int16(integral1(:,:,2)),['MTRIntegral.dcm'],'grayscale','63355');   % 保存MTR积分图像
                        dicomwrite(int16(integral1(:,:,5)),['APTIntegral.dcm'],'grayscale','63355');   % 保存APT积分图像
                        dicomwrite(int16(integral1(:,:,1)),['NOEIntegral.dcm'],'grayscale','63355');   % 保存NOE积分图像
                        dicomwrite(int16(integral1(:,:,4)),['CrIntegral.dcm'],'grayscale','63355');    % 保存Cr积分图像

                        % 删除多余的绘图命令，避免创建空白窗口

                        % 保存五池位置参数
                        dicomwrite(int16(pos1(:,:,3)),['WaterPosition.dcm'],'grayscale','63355');  % 保存水峰位置图像
                        dicomwrite(int16(pos1(:,:,2)),['MTRPosition.dcm'],'grayscale','63355');    % 保存MTR位置图像
                        dicomwrite(int16(pos1(:,:,5)),['APTPosition.dcm'],'grayscale','63355');    % 保存APT位置图像
                        dicomwrite(int16(pos1(:,:,1)),['NOEPosition.dcm'],'grayscale','63355');    % 保存NOE位置图像
                        dicomwrite(int16(pos1(:,:,4)),['CrPosition.dcm'],'grayscale','63355');     % 保存Cr位置图像

                        % 保存五池宽度参数
                        dicomwrite(int16(width1(:,:,3)),['WaterWidth.dcm'],'grayscale','63355');   % 保存水峰宽度图像
                        dicomwrite(int16(width1(:,:,2)),['MTRWidth.dcm'],'grayscale','63355');     % 保存MTR宽度图像
                        dicomwrite(int16(width1(:,:,5)),['APTWidth.dcm'],'grayscale','63355');     % 保存APT宽度图像
                        dicomwrite(int16(width1(:,:,1)),['NOEWidth.dcm'],'grayscale','63355');     % 保存NOE宽度图像
                        dicomwrite(int16(width1(:,:,4)),['CrWidth.dcm'],'grayscale','63355');      % 保存Cr宽度图像
                        % 将15个参数图像保存到pTarget结构体
                        pTarget.WaterAmplitude = ampl1(:,:,3);
                        pTarget.MTRAmplitude   = ampl1(:,:,2);
                        pTarget.APTAmplitude   = ampl1(:,:,5);
                        pTarget.NOEAmplitude   = ampl1(:,:,1);
                        pTarget.CrAmplitude    = ampl1(:,:,4);
                        pTarget.WaterIntegral  = integral1(:,:,3);
                        pTarget.MTRIntegral    = integral1(:,:,2);
                        pTarget.APTIntegral    = integral1(:,:,5);
                        pTarget.NOEIntegral    = integral1(:,:,1);
                        pTarget.CrIntegral     = integral1(:,:,4);
                        pTarget.WaterPosition  = pos1(:,:,3);
                        pTarget.MTRPosition    = pos1(:,:,2);
                        pTarget.APTPosition    = pos1(:,:,5);
                        pTarget.NOEPosition    = pos1(:,:,1);
                        pTarget.CrPosition     = pos1(:,:,4);
                        pTarget.WaterWidth     = width1(:,:,3);
                        pTarget.MTRWidth       = width1(:,:,2);
                        pTarget.APTWidth       = width1(:,:,5);
                        pTarget.NOEWidth       = width1(:,:,1);
                        pTarget.CrWidth        = width1(:,:,4);
                        % 计算offs变量，转为行向量
                        offs = (FreqList/128)'; % 频率文件数据除以128，1xM
                        % 提取ROI掩膜内像素并保存到zInput变量
                        roi_idx = find(mask44 > 0);                % 掩膜内像素的线性索引
                        num_slices = size(data, 3);                % 实际的层数
                        num_pixels = numel(roi_idx);               % 掩膜内像素数
                        zInput = zeros(num_slices, num_pixels);    % 初始化ROI数据矩阵
                        for k = 1:num_slices
                            slice_img = data(:,:,k);
                            zInput(k, :) = slice_img(roi_idx);     % 提取每层ROI像素
                        end
                        % 构建15xN的pTarget矩阵，避免链式索引
                        pTarget = zeros(15, num_pixels);
                        % 幅值
                        tmp = ampl1(:,:,3); pTarget(1,:)  = tmp(roi_idx)'; % WaterAmplitude
                        tmp = ampl1(:,:,2); pTarget(2,:)  = tmp(roi_idx)'; % MTRAmplitude
                        tmp = ampl1(:,:,5); pTarget(3,:)  = tmp(roi_idx)'; % APTAmplitude
                        tmp = ampl1(:,:,1); pTarget(4,:)  = tmp(roi_idx)'; % NOEAmplitude
                        tmp = ampl1(:,:,4); pTarget(5,:)  = tmp(roi_idx)'; % CrAmplitude
                        % 积分
                        tmp = integral1(:,:,3);  pTarget(6,:)  = tmp(roi_idx)';
                        tmp = integral1(:,:,2);  pTarget(7,:)  = tmp(roi_idx)';
                        tmp = integral1(:,:,5);  pTarget(8,:)  = tmp(roi_idx)';
                        tmp = integral1(:,:,1);  pTarget(9,:)  = tmp(roi_idx)';
                        tmp = integral1(:,:,4);  pTarget(10,:) = tmp(roi_idx)';
                        % 位置
                        tmp = pos1(:,:,3);  pTarget(11,:) = tmp(roi_idx)';
                        tmp = pos1(:,:,2);  pTarget(12,:) = tmp(roi_idx)';
                        tmp = pos1(:,:,5);  pTarget(13,:) = tmp(roi_idx)';
                        tmp = pos1(:,:,1);  pTarget(14,:) = tmp(roi_idx)';
                        tmp = pos1(:,:,4);  pTarget(15,:) = tmp(roi_idx)';
                        % 如需宽度参数可继续添加
                        % 生成的mat文件保存在SavePath目录下，文件名与nii文件前缀一致
                        niiName = [SeqName, '.nii'];
                        [~, niiPrefix, ~] = fileparts(niiName);    % 获取nii文件前缀
                        matName = [niiPrefix, '.mat'];             % 生成的mat文件名
                        save(fullfile(SavePath, matName), 'pTarget', 'offs', 'zInput');
                        disp(['mat文件已保存: ', fullfile(SavePath, matName)]);
                    end
                end
            end
        else
            disp(['data in ',FileName(i).name,'  ',FileName2(j).name,' already processed']); % 已处理过的数据直接跳过
        end
    end
end
end
