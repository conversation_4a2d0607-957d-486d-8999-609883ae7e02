# 📁 3T→7T跨场强DeepCEST训练系统 - 集中式管理

## 🚀 **快速开始（一键运行）**

```matlab
% 进入训练模型文件夹，按顺序执行所有步骤：
cd('C:\Users\<USER>\Documents\augment-projects\1\训练模型')

% 第0步：生成演示数据（5池拟合）
run('步骤0_FivePoolsLorentz_FittingTestForSingleDataset.m')

% 第1-4步：完整训练流程
run('步骤1_整理拟合数据_organize_fitted_data.m')
run('步骤2_批量处理数据_batch_process_organized_data.m')
run('步骤3_训练模型_DeepCEST_training_5pool.m')
run('步骤4_模型推理_DeepCEST_5pool_inference.m')
```

## 🎯 **核心文件（按使用顺序）**

### **步骤0：5池洛伦兹拟合** ⭐ **真实拟合脚本**
📄 `步骤0_FivePoolsLorentz_FittingTestForSingleDataset.m`
- 功能：读取NII数据，执行真实的5池洛伦兹拟合
- 输入：Data/患者ID/检查时间/APT_CEST_15.nii
- 输出：A4CestParam5Pools_48points_APT_CEST_15/患者ID/检查时间/*.mat
- 格式：zInput [48×N_voxels], pTarget [15×N_voxels], offs [48×1]
- **工具箱**：CestToolBox/ (已复制到训练模型文件夹)

### **步骤1：整理拟合数据**
📄 `步骤1_整理拟合数据_organize_fitted_data.m`
- 功能：从拟合结果文件夹复制MAT文件到训练文件夹
- 输入：A4CestParam5Pools_48points_APT_CEST_15/患者ID/检查时间/*.mat
- 输出：Data/患者ID/检查时间/拟合数据.mat

### **步骤2：批量处理数据**
📄 `步骤2_批量处理数据_batch_process_organized_data.m`
- 功能：合并所有患者数据并生成7T标签（内置物理模型）
- 输出：DeepCEST_5Pool_OrganizedData_TrainData.mat

### **步骤3：训练神经网络**
📄 `步骤3_训练模型_DeepCEST_training_5pool.m`
- 功能：训练150×150×150的三层神经网络
- 输出：Networks/CESTNet_3T_to_7T_*.mat

### **步骤4：模型推理测试**
📄 `步骤4_模型推理_DeepCEST_5pool_inference.m`
- 功能：使用训练好的网络进行3T→7T参数预测
- 输出：Results/DeepCEST_3T_to_7T_Results.mat + 15个DCM文件

## 📊 **数据格式说明**

### **支持的输入格式**
- **2D向量格式**：zInput [48×N_voxels], pTarget [15×N_voxels]
- **4D图像格式**：zSpec [H×W×S×N_freq], mask [H×W×S]
- **自动检测转换**：系统自动识别并转换格式

### **输出格式**
- **训练数据**：DeepCEST_5Pool_OrganizedData_TrainData.mat
- **网络模型**：Networks/CESTNet_3T_to_7T_*.mat
- **推理结果**：Results/DeepCEST_3T_to_7T_Results.mat
- **DCM文件**：Results/DCM_ParameterMaps/*.dcm (15个参数图)

## 🔧 **数据准备要求**

### **输入数据格式**
1. **NII文件位置**：Data/患者ID/检查时间/APT_CEST_15.nii
2. **数据要求**：48层CEST图像，对应48个频率点
3. **频率文件**：CestToolBox/CEST_freq_48points.txt

### **工具箱功能**
**CestToolBox/** - CEST拟合工具箱：
- **curvefitZspect2ppmForPlot.m**：主要的5池洛伦兹拟合函数
- **rest_ReadNiftiImage.m**：NII文件读取函数（需要REST工具箱）
- **CEST_freq_48points.txt**：48个频率点配置文件

**Toolbox/** - 可视化工具箱：
- **mycolormap.m**：自定义颜色映射函数
- **MyColormaps1.mat, MyColormaps2.mat, MyColormaps3.mat**：预设颜色映射数据

### **拟合参数说明**
- **5个池**：NOE(-3.6ppm), MT(-1.5ppm), Water(0ppm), Cr(2.04ppm), APT(3.5ppm)
- **3个参数**：每个池的振幅、积分、位置
- **输出格式**：15个参数 = 5池 × 3参数

## 📊 **生成的文件夹结构**

```
训练模型/
├── 步骤0_FivePoolsLorentz_FittingTestForSingleDataset.m
├── 步骤1_整理拟合数据_organize_fitted_data.m
├── 步骤2_批量处理数据_batch_process_organized_data.m
├── 步骤3_训练模型_DeepCEST_training_5pool.m
├── 步骤4_模型推理_DeepCEST_5pool_inference.m
├── CestToolBox/                             # CEST拟合工具箱
├── Toolbox/                                 # 可视化工具箱
├── Data/                                    # 所有数据文件 ⭐
│   ├── 患者ID/检查时间/APT_CEST_15.nii      # CEST NII输入文件
│   └── 患者ID/检查时间/拟合数据.mat          # 整理后的训练数据
├── A4CestParam5Pools_48points_APT_CEST_15/  # 拟合结果输出
│   └── 患者ID/检查时间/*.mat
├── Networks/                                # 训练好的网络
│   └── CESTNet_3T_to_7T_*.mat
├── Results/                                 # 推理结果
│   ├── DeepCEST_3T_to_7T_Results.mat       # MAT格式结果
│   └── DCM_ParameterMaps/                   # DCM格式参数图 ⭐
│       ├── WaterAmplitude.dcm               # 水池振幅
│       ├── MTRAmplitude.dcm                 # MTR振幅
│       ├── APTAmplitude.dcm                 # APT振幅
│       ├── NOEAmplitude.dcm                 # NOE振幅
│       ├── CrAmplitude.dcm                  # Cr振幅
│       ├── ... (积分和位置参数)
│       └── ParameterMap_Summary.txt         # 参数汇总信息
└── DeepCEST_5Pool_OrganizedData_TrainData.mat  # 合并训练数据
```

## ✨ **系统特点**

- 🧲 **跨场强预测** - 从3T数据预测7T质量参数
- 🇨🇳 **完全中文化** - 所有界面和注释都是中文
- 📝 **分步骤操作** - 1234步骤，清晰明了
- 🧪 **物理模型驱动** - 基于CEST物理原理生成7T标签
- 🔄 **自动化处理** - 一键完成复杂操作
- 🔍 **智能格式检测** - 自动识别2D向量或4D图像格式
- 🔄 **无缝格式转换** - 2D↔4D数据格式自动转换
- 📊 **详细反馈** - 每步都有进度提示
- 🛠️ **可定制配置** - 支持参数调整

**现在您可以用3T设备获得7T质量的CEST参数了！** 🚀🧲
