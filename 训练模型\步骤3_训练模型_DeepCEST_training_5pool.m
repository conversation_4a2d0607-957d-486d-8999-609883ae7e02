%% 步骤3：训练3T→7T跨场强DeepCEST神经网络模型
% Step 3: Train 3T→7T Cross-Field DeepCEST Neural Network Model
%
% 功能说明：
% 1. 自动检测并加载训练数据（3T输入，7T标签）
% 2. 配置神经网络架构和训练参数
% 3. 训练15参数（5池×3参数）3T→7T跨场强预测模型
% 4. 评估训练性能并保存模型
%
% 使用方法：
% cd('C:\Users\<USER>\Documents\augment-projects\1\训练模型')
% run('步骤3_训练模型_DeepCEST_training_5pool.m')

clear all; close all; clc; warning off;
addpath(genpath(pwd));  % 添加当前路径及子路径

% 添加工具箱路径
% Add toolbox paths
if exist('Toolbox', 'dir')
    addpath(genpath('Toolbox'));
    fprintf('✅ 已添加Toolbox工具箱路径\n');
end
if exist('CestToolBox', 'dir')
    addpath(genpath('CestToolBox'));
    fprintf('✅ 已添加CestToolBox工具箱路径\n');
end

fprintf('=== 步骤3：训练3T→7T跨场强DeepCEST神经网络模型 ===\n');
fprintf('=== Step 3: Train 3T→7T Cross-Field DeepCEST Neural Network Model ===\n\n');

%% 第1部分：自动检测并加载3T→7T训练数据 - Auto-detect and Load 3T→7T Training Data
fprintf('>> 第1部分：自动检测并加载3T→7T训练数据\n');

% 按优先级检测可用的训练数据文件
% Detect available training data files by priority
organized_data_file = 'DeepCEST_5Pool_OrganizedData_TrainData.mat';      % 组织化多患者数据
real_data_file = 'Data/DeepCEST_5Pool_YourRealData_TrainData.mat';       % 单患者真实数据
synthetic_data_file = ['Data', filesep, 'DeepCEST_5Pool_TrainData.mat']; % 合成数据

if exist(organized_data_file, 'file')
    % 加载组织化多患者数据（最优选择）
    % Load organized multi-patient data (best choice)
    load(organized_data_file);
    fprintf('   ✅ 已加载3T→7T跨场强训练数据\n');
    fprintf('   ✅ Loaded 3T→7T cross-field training data\n');
    fprintf('   输入: 3T Z谱数据, %d 个患者-检查时间, %d 个总像素\n', successful_count, size(zInput, 2));
    fprintf('   Input: 3T Z-spectra, %d patient-sessions, %d total voxels\n', successful_count, size(zInput, 2));
    fprintf('   标签: 7T CEST参数 (基于物理模型生成)\n');
    fprintf('   Labels: 7T CEST parameters (generated from physics model)\n');
    
elseif exist(real_data_file, 'file')
    % 加载单患者真实数据
    % Load single-patient real data
    load(real_data_file);
    fprintf('   ✅ 已加载单患者真实CEST数据用于训练\n');
    fprintf('   ✅ Loaded single-patient real CEST data for training\n');
    fprintf('   数据集: %d 个像素\n', size(zInput, 2));
    fprintf('   Dataset: %d voxels\n', size(zInput, 2));
    
elseif exist(synthetic_data_file, 'file')
    % 加载合成数据
    % Load synthetic data
    load(synthetic_data_file);
    fprintf('   ✅ 使用合成训练数据\n');
    fprintf('   ✅ Using synthetic training data\n');
    fprintf('   数据集: %d 个合成样本\n', size(zInput, 2));
    fprintf('   Dataset: %d synthetic samples\n', size(zInput, 2));
    
else
    error('❌ 没有找到训练数据！请先运行数据准备脚本。\nNo training data found! Please run one of the data preparation scripts first.');
end

%% 第2部分：验证数据维度 - Verify Data Dimensions
fprintf('\n>> 第2部分：验证数据维度\n');

fprintf('   输入数据形状: %s\n', mat2str(size(zInput)));
fprintf('   Input data shape: %s\n', mat2str(size(zInput)));
fprintf('   目标数据形状: %s\n', mat2str(size(pTarget)));
fprintf('   Target data shape: %s\n', mat2str(size(pTarget)));
fprintf('   频率点数: %d\n', length(offs));
fprintf('   Number of frequency points: %d\n', length(offs));
fprintf('   训练样本数: %d\n', size(zInput, 2));
fprintf('   Number of training samples: %d\n', size(zInput, 2));

% 验证参数数量
% Verify parameter count
if size(pTarget, 1) ~= 15
    error('❌ 期望15个参数（5池×3参数），实际得到 %d 个\nExpected 15 parameters (5 pools × 3 params), got %d', size(pTarget, 1));
end

%% 第3部分：配置神经网络架构 - Configure Neural Network Architecture
fprintf('\n>> 第3部分：配置神经网络架构\n');

% 训练算法
% Training algorithm
trainFcn = 'trainscg';  % 缩放共轭梯度算法 - Scaled conjugate gradient

% 隐藏层结构（针对15参数优化的更大网络）
% Hidden layer structure (larger network optimized for 15 parameters)
hiddenLayerSize = [150, 150, 150]; % 3层，每层150个神经元
hiddenLayNum = size(hiddenLayerSize, 2);

fprintf('   训练算法: %s\n', trainFcn);
fprintf('   Training algorithm: %s\n', trainFcn);
fprintf('   隐藏层结构: %s\n', mat2str(hiddenLayerSize));
fprintf('   Hidden layer structure: %s\n', mat2str(hiddenLayerSize));

% 创建前馈神经网络
% Create feedforward neural network
net = fitnet(hiddenLayerSize, trainFcn);

%% 第4部分：配置数据分割 - Configure Data Division
fprintf('\n>> 第4部分：配置数据分割\n');

% 数据分割方式
% Data division
net.divideFcn = 'dividerand';           % 随机分割
net.divideParam.trainRatio = 70/100;    % 70% 用于训练
net.divideParam.valRatio = 15/100;      % 15% 用于验证
net.divideParam.testRatio = 15/100;     % 15% 用于测试

fprintf('   数据分割: 训练 %d%%, 验证 %d%%, 测试 %d%%\n', ...
        net.divideParam.trainRatio*100, net.divideParam.valRatio*100, net.divideParam.testRatio*100);
fprintf('   Data division: Train %d%%, Validation %d%%, Test %d%%\n', ...
        net.divideParam.trainRatio*100, net.divideParam.valRatio*100, net.divideParam.testRatio*100);

%% 第5部分：配置网络架构细节 - Configure Network Architecture Details
fprintf('\n>> 第5部分：配置网络架构细节\n');

% 隐藏层激活函数
% Hidden layer activation functions
for nn = 1:hiddenLayNum
    net.layers{nn}.transferFcn = 'tansig';  % 双曲正切激活函数
end

% 输入/输出处理
% Input/Output processing
net.output.processFcns = {'mapminmax'};  % 输出归一化到[-1,1]

fprintf('   激活函数: tansig (双曲正切)\n');
fprintf('   Activation function: tansig (hyperbolic tangent)\n');
fprintf('   输出处理: mapminmax 归一化\n');
fprintf('   Output processing: mapminmax normalization\n');

%% 第6部分：配置训练参数 - Configure Training Parameters
fprintf('\n>> 第6部分：配置训练参数\n');

% 训练参数设置
% Training parameter settings
net.trainParam.show = 50;           % 每50轮显示一次进度
net.trainParam.lr = 1e-3;           % 学习率
net.trainParam.epochs = 2e4;        % 最大训练轮数（针对复杂模型增加）
net.trainParam.goal = 1e-5;         % 目标误差（更严格的目标）
net.trainParam.max_fail = 30;       % 验证失败容忍次数（更多耐心）
net.trainParam.min_grad = 1e-6;     % 最小梯度

fprintf('   学习率: %.0e\n', net.trainParam.lr);
fprintf('   Learning rate: %.0e\n', net.trainParam.lr);
fprintf('   最大训练轮数: %d\n', net.trainParam.epochs);
fprintf('   Maximum epochs: %d\n', net.trainParam.epochs);
fprintf('   目标误差: %.0e\n', net.trainParam.goal);
fprintf('   Target error: %.0e\n', net.trainParam.goal);

%% 第7部分：配置性能函数 - Configure Performance Function
fprintf('\n>> 第7部分：配置性能函数\n');

% 性能函数（带正则化的均方误差）
% Performance function (MSE with regularization)
net.performFcn = 'msereg';                      % 正则化均方误差
net.performParam.regularization = 0.005;       % 正则化系数（稍微降低以适应更复杂模型）
net.performParam.normalization = 'percent';    % 百分比归一化

fprintf('   性能函数: msereg (正则化均方误差)\n');
fprintf('   Performance function: msereg (regularized MSE)\n');
fprintf('   正则化系数: %.3f\n', net.performParam.regularization);
fprintf('   Regularization coefficient: %.3f\n', net.performParam.regularization);

%% 第8部分：开始训练网络 - Start Network Training
fprintf('\n=== 第8部分：开始训练神经网络 ===\n');
fprintf('=== Part 8: Start Neural Network Training ===\n');

fprintf('正在开始训练...\n');
fprintf('Starting training...\n');
fprintf('预计训练时间: 2-4小时（取决于数据量和硬件）\n');
fprintf('Estimated training time: 2-4 hours (depending on data size and hardware)\n\n');

% 记录训练开始时间
% Record training start time
tic;

% 训练网络（使用并行计算，不使用GPU）
% Train network (use parallel computing, no GPU)
[net, tr] = train(net, zInput, pTarget, 'useParallel', 'yes', 'useGPU', 'no');

% 计算训练时间
% Calculate training time
training_time = toc;
fprintf('\n训练完成！用时 %.2f 秒 (%.2f 分钟)\n', training_time, training_time/60);
fprintf('Training completed in %.2f seconds (%.2f minutes)\n', training_time, training_time/60);

%% 第9部分：网络预测和性能评估 - Network Prediction and Performance Evaluation
fprintf('\n=== 第9部分：网络预测和性能评估 ===\n');
fprintf('=== Part 9: Network Prediction and Performance Evaluation ===\n');

% 对测试集进行预测
% Predict on test set
fprintf('正在进行网络预测...\n');
fprintf('Performing network prediction...\n');

outputs_test = net(zInput(:, tr.testInd), 'useParallel', 'yes', 'useGPU', 'no');
outputs_all = net(zInput, 'useParallel', 'yes', 'useGPU', 'no');

% 计算性能指标
% Calculate performance metrics
fprintf('正在计算性能指标...\n');
fprintf('Calculating performance metrics...\n');

test_mse = mean((outputs_test - pTarget(:, tr.testInd)).^2, 2);  % 每个参数的均方误差
test_r2 = zeros(15, 1);  % R²决定系数

% 计算每个参数的R²值
% Calculate R² for each parameter
for i = 1:15
    y_true = pTarget(i, tr.testInd);    % 真实值
    y_pred = outputs_test(i, :);        % 预测值
    ss_res = sum((y_true - y_pred).^2); % 残差平方和
    ss_tot = sum((y_true - mean(y_true)).^2); % 总平方和
    test_r2(i) = 1 - ss_res/ss_tot;     % R²计算
end

%% 第10部分：显示性能结果 - Display Performance Results
fprintf('\n=== 第10部分：显示性能结果 ===\n');
fprintf('=== Part 10: Display Performance Results ===\n');

% 参数名称（中英文对照）
% Parameter names (Chinese-English)
param_names_cn = {
    '水池振幅/Water Amplitude', 'MTR振幅/MTR Amplitude', 'APT振幅/APT Amplitude', 'NOE振幅/NOE Amplitude', 'Cr振幅/Cr Amplitude', ...
    '水池积分/Water Integral', 'MTR积分/MTR Integral', 'APT积分/APT Integral', 'NOE积分/NOE Integral', 'Cr积分/Cr Integral', ...
    '水池位置/Water Position', 'MTR位置/MTR Position', 'APT位置/APT Position', 'NOE位置/NOE Position', 'Cr位置/Cr Position'
};

fprintf('\n性能总结:\n');
fprintf('Performance Summary:\n');
for i = 1:15
    fprintf('参数 %2d (%s): MSE=%.6f, R²=%.4f\n', ...
            i, param_names_cn{i}, test_mse(i), test_r2(i));
end

% 计算整体性能
% Calculate overall performance
overall_mse = mean(test_mse);
overall_r2 = mean(test_r2);

fprintf('\n整体性能:\n');
fprintf('Overall Performance:\n');
fprintf('  平均MSE: %.6f\n', overall_mse);
fprintf('  Average MSE: %.6f\n', overall_mse);
fprintf('  平均R²: %.4f\n', overall_r2);
fprintf('  Average R²: %.4f\n', overall_r2);

%% 第11部分：创建性能可视化 - Create Performance Visualization
fprintf('\n=== 第11部分：创建性能可视化 ===\n');
fprintf('=== Part 11: Create Performance Visualization ===\n');

% 设置图形背景为白色
% Set figure background to white
set(0, 'defaultfigurecolor', 'w');

% 绘制关键参数的回归结果
% Plot regression results for key parameters
key_params = [2, 4, 10, 13]; % B0偏移, 酰胺交换, rNOE交换, MT振幅
key_names_cn = {'B0偏移/B0 Offset', '酰胺交换/Amide Exchange', 'rNOE交换/rNOE Exchange', 'MT振幅/MT Amplitude'};

figure('Position', [100, 100, 1200, 800]);
for i = 1:4
    param_idx = key_params(i);
    subplot(2, 2, i);
    
    % 获取真实值和预测值
    % Get true and predicted values
    y_true = gather(pTarget(param_idx, tr.testInd));
    y_pred = gather(outputs_test(param_idx, :));
    
    % 绘制散点图
    % Plot scatter plot
    scatter(y_true, y_pred, 20, 'filled', 'Alpha', 0.6);
    hold on;
    
    % 完美预测线（y=x）
    % Perfect prediction line (y=x)
    lims = [min([y_true, y_pred]), max([y_true, y_pred])];
    plot(lims, lims, 'r--', 'LineWidth', 2, 'DisplayName', '完美预测/Perfect');
    
    % 线性拟合线
    % Linear fit line
    p = polyfit(y_true, y_pred, 1);
    plot(lims, polyval(p, lims), 'b-', 'LineWidth', 1.5, 'DisplayName', '线性拟合/Linear Fit');
    
    xlabel('真实值/True Values');
    ylabel('预测值/Predicted Values');
    title(sprintf('%s (R² = %.3f)', key_names_cn{i}, test_r2(param_idx)));
    grid on;
    axis equal;
    xlim(lims);
    ylim(lims);
    legend('show');
end

sgtitle('5池DeepCEST关键参数预测性能 / 5-Pool DeepCEST Key Parameter Prediction Performance');

fprintf('   ✅ 性能可视化图表已创建\n');
fprintf('   ✅ Performance visualization created\n');

%% 第12部分：保存训练好的网络 - Save Trained Network
fprintf('\n=== 第12部分：保存训练好的网络 ===\n');
fprintf('=== Part 12: Save Trained Network ===\n');

% 创建Networks文件夹（如果不存在）
% Create Networks folder if it doesn't exist
if ~exist('Networks', 'dir')
    mkdir('Networks');
    fprintf('   已创建Networks文件夹\n');
    fprintf('   Created Networks folder\n');
end

% 生成保存文件名（包含网络结构和性能信息）
% Generate save filename (including network structure and performance info)
layName = sprintf('_%d', hiddenLayerSize);                          % 网络层结构
gamaStr = sprintf('_%.3f', net.performParam.regularization);       % 正则化系数
epochStr = sprintf('_%d', tr.best_epoch);                           % 最佳训练轮数
vperfStr = sprintf('_%.6f', tr.best_vperf);                         % 最佳验证性能

save_name = ['Networks', filesep, 'CESTNet_3T_to_7T', layName, gamaStr, '_', epochStr, '_', vperfStr, '.mat'];

% 保存网络和相关信息
% Save network and related information
save(save_name, 'net', 'tr', 'param_names_cn', 'test_mse', 'test_r2', ...
     'training_time', 'overall_mse', 'overall_r2');

fprintf('   ✅ 网络已保存为: %s\n', save_name);
fprintf('   ✅ Network saved as: %s\n', save_name);

%% 第13部分：显示训练进度 - Display Training Progress
fprintf('\n=== 第13部分：显示训练进度 ===\n');
fprintf('=== Part 13: Display Training Progress ===\n');

% 绘制训练进度图
% Plot training progress
figure('Position', [100, 100, 800, 600]);
plotperform(tr);
title('3T→7T跨场强DeepCEST训练进度 / 3T→7T Cross-Field DeepCEST Training Progress');
xlabel('训练轮数 / Epochs');
ylabel('性能 (MSE) / Performance (MSE)');

fprintf('   ✅ 训练进度图表已创建\n');
fprintf('   ✅ Training progress chart created\n');

%% 第14部分：训练总结报告 - Training Summary Report
fprintf('\n=== 第14部分：训练总结报告 ===\n');
fprintf('=== Part 14: Training Summary Report ===\n');

fprintf('训练总结:\n');
fprintf('Training Summary:\n');
fprintf('  最佳训练轮数: %d\n', tr.best_epoch);
fprintf('  Best epoch: %d\n', tr.best_epoch);
fprintf('  最佳验证性能: %.6f\n', tr.best_vperf);
fprintf('  Best validation performance: %.6f\n', tr.best_vperf);
fprintf('  最终训练性能: %.6f\n', tr.perf(end));
fprintf('  Final training performance: %.6f\n', tr.perf(end));
fprintf('  训练用时: %.2f 秒 (%.2f 分钟)\n', training_time, training_time/60);
fprintf('  Training time: %.2f seconds (%.2f minutes)\n', training_time, training_time/60);

% 性能等级评估
% Performance level assessment
fprintf('\n性能等级评估:\n');
fprintf('Performance Level Assessment:\n');

excellent_params = sum(test_r2 > 0.9);   % 优秀 (R² > 0.9)
good_params = sum(test_r2 > 0.8 & test_r2 <= 0.9);  % 良好 (0.8 < R² ≤ 0.9)
fair_params = sum(test_r2 > 0.7 & test_r2 <= 0.8);  % 一般 (0.7 < R² ≤ 0.8)
poor_params = sum(test_r2 <= 0.7);       % 较差 (R² ≤ 0.7)

fprintf('  优秀参数 (R² > 0.9): %d 个\n', excellent_params);
fprintf('  Excellent parameters (R² > 0.9): %d\n', excellent_params);
fprintf('  良好参数 (0.8 < R² ≤ 0.9): %d 个\n', good_params);
fprintf('  Good parameters (0.8 < R² ≤ 0.9): %d\n', good_params);
fprintf('  一般参数 (0.7 < R² ≤ 0.8): %d 个\n', fair_params);
fprintf('  Fair parameters (0.7 < R² ≤ 0.8): %d\n', fair_params);
fprintf('  较差参数 (R² ≤ 0.7): %d 个\n', poor_params);
fprintf('  Poor parameters (R² ≤ 0.7): %d\n', poor_params);

%% 第15部分：下一步建议 - Next Steps Recommendations
fprintf('\n=== 第15部分：下一步建议 ===\n');
fprintf('=== Part 15: Next Steps Recommendations ===\n');

fprintf('下一步建议:\n');
fprintf('Next Steps Recommendations:\n');

if overall_r2 > 0.85
    fprintf('  ✅ 训练效果优秀！可以进行推理测试\n');
    fprintf('  ✅ Excellent training results! Ready for inference testing\n');
    fprintf('  建议运行: 步骤4_模型推理_DeepCEST_5pool_inference.m\n');
    fprintf('  Recommended: Run 步骤4_模型推理_DeepCEST_5pool_inference.m\n');
elseif overall_r2 > 0.75
    fprintf('  ✅ 训练效果良好！可以进行推理测试\n');
    fprintf('  ✅ Good training results! Can proceed to inference testing\n');
    fprintf('  建议运行: 步骤4_模型推理_DeepCEST_5pool_inference.m\n');
    fprintf('  Recommended: Run 步骤4_模型推理_DeepCEST_5pool_inference.m\n');
else
    fprintf('  ⚠️  训练效果一般，建议优化:\n');
    fprintf('  ⚠️  Training results are fair, consider optimization:\n');
    fprintf('  1. 增加训练数据量\n');
    fprintf('  1. Increase training data size\n');
    fprintf('  2. 调整网络结构\n');
    fprintf('  2. Adjust network architecture\n');
    fprintf('  3. 优化训练参数\n');
    fprintf('  3. Optimize training parameters\n');
end

fprintf('\n可选的后续步骤:\n');
fprintf('Optional follow-up steps:\n');
fprintf('1. 模型推理测试: 步骤4_模型推理_DeepCEST_5pool_inference.m\n');
fprintf('1. Model inference testing: 步骤4_模型推理_DeepCEST_5pool_inference.m\n');
fprintf('2. 添加更多患者数据重新训练\n');
fprintf('2. Add more patient data and retrain\n');
fprintf('3. 调整网络参数进行优化\n');
fprintf('3. Adjust network parameters for optimization\n');

fprintf('\n=== 步骤3完成 ===\n');
fprintf('=== Step 3 Complete ===\n');
fprintf('3T→7T跨场强DeepCEST模型训练完成！\n');
fprintf('3T→7T Cross-Field DeepCEST model training completed!\n');
