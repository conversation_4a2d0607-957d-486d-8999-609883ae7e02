%% 步骤4：3T→7T跨场强DeepCEST模型推理测试
% Step 4: 3T→7T Cross-Field DeepCEST Model Inference Testing
%
% 功能说明：
% 1. 加载训练好的3T→7T跨场强DeepCEST网络
% 2. 对3T CEST数据进行7T参数预测
% 3. 生成15个7T参数图
% 4. 保存推理结果
%
% 使用方法：
% cd('C:\Users\<USER>\Documents\augment-projects\1\训练模型')
% run('步骤4_模型推理_DeepCEST_5pool_inference.m')

clear all; close all; clc; warning off;
addpath(genpath(pwd));  % 添加当前路径及子路径

% 添加工具箱路径
% Add toolbox paths
if exist('Toolbox', 'dir')
    addpath(genpath('Toolbox'));
    fprintf('✅ 已添加Toolbox工具箱路径\n');
end
if exist('CestToolBox', 'dir')
    addpath(genpath('CestToolBox'));
    fprintf('✅ 已添加CestToolBox工具箱路径\n');
end

fprintf('=== 步骤4：3T→7T跨场强DeepCEST模型推理测试 ===\n');
fprintf('=== Step 4: 3T→7T Cross-Field DeepCEST Model Inference Testing ===\n\n');

%% 第1部分：加载训练好的网络 - Load Trained Network
fprintf('>> 第1部分：加载训练好的网络\n');

% 查找训练好的网络文件
% Find trained network files
network_pattern = 'Networks/CESTNet_3T_to_7T_150_150_150_0.005_*.mat';
network_files = dir(network_pattern);

if isempty(network_files)
    error('❌ 没有找到训练好的3T→7T网络。请先运行步骤3训练网络！\nNo trained 3T→7T network found. Please run Step 3 to train the network first!');
end

% 加载最新的网络
% Load the most recent network
[~, idx] = max([network_files.datenum]);
network_path = fullfile(network_files(idx).folder, network_files(idx).name);
load(network_path);

fprintf('   ✅ 已加载网络: %s\n', network_files(idx).name);
fprintf('   ✅ Loaded network: %s\n', network_files(idx).name);
fprintf('   网络架构: %s\n', mat2str([net.layers{1:end-1}.size]));
fprintf('   Network architecture: %s\n', mat2str([net.layers{1:end-1}.size]));

%% 第2部分：加载3T CEST数据进行7T参数推理 - Load 3T CEST Data for 7T Parameter Inference
fprintf('\n>> 第2部分：加载3T CEST数据进行7T参数推理\n');

% 替换此部分为您的实际数据加载
% Replace this section with your actual data loading
% 期望格式:
% Expected format:
% - zSpec: [height × width × slices × frequency_points] - 您的Z谱数据
% - mask: [height × width × slices] - 脑掩膜 (1=脑组织, 0=背景)
% - offs: [frequency_points × 1] - 频率偏移 (应与训练时匹配)

% 示例：加载演示数据（请替换为您的数据文件）
% Example: Load demo data (replace with your data file)
data_file = 'Data/YourCESTData.mat'; % 替换为您的数据文件路径

% 如果您想使用原始CEST项目的WTdemo.mat数据，可以这样：
% data_file = 'C:\Users\<USER>\Documents\augment-projects\SUCHANGLIANG20250616\CEST_DATAPROCESSING\当前\Synthesis9tCESTFrom3T\01DebugedCodes\DeepCESTandDeepAREX-main\Data\WTdemo.mat';

if exist(data_file, 'file')
    load(data_file);
    fprintf('   ✅ 已从以下位置加载CEST数据: %s\n', data_file);
    fprintf('   ✅ Loaded CEST data from: %s\n', data_file);

    % 检测数据格式并进行转换
    % Detect data format and convert if necessary
    [zSpec, mask, ppm_offsets, data_format] = detect_and_convert_data_format_internal();
else
    % 生成合成测试数据用于演示
    % Generate synthetic test data for demonstration
    fprintf('   ⚠️  没有找到真实数据，正在生成合成测试数据用于演示...\n');
    fprintf('   ⚠️  No real data found, generating synthetic test data for demonstration...\n');

    % 加载训练数据以获取频率偏移
    % Load training data to get frequency offsets
    if exist('DeepCEST_5Pool_OrganizedData_TrainData.mat', 'file')
        load('DeepCEST_5Pool_OrganizedData_TrainData.mat', 'offs', 'ppm_offsets');
    elseif exist('Data/DeepCEST_5Pool_TrainData.mat', 'file')
        load('Data/DeepCEST_5Pool_TrainData.mat', 'offs', 'ppm_offsets');
    else
        error('❌ 找不到训练数据来获取频率信息\nCannot find training data to get frequency information');
    end

    % 生成合成数据并设置格式标识
    [zSpec, mask, pmp_offsets] = generate_synthetic_cest_data_internal(offs, pmp_offsets);
    data_format = '4D_synthetic';
    
    % 这部分代码已移动到内部函数中
    % This code has been moved to internal function
    
    for i = 1:height
        for j = 1:width
            if sqrt((i-32)^2 + (j-32)^2) < 25 % 脑区域
                % 模拟真实的Z谱
                % Simulate realistic Z-spectrum
                for f = 1:n_freq
                    freq_ppm = ppm_offsets(f);
                    z_val = 1.0;  % 基线值
                    
                    % 添加CEST效应
                    % Add CEST effects
                    if abs(freq_ppm - 3.5) < 0.5  % 酰胺效应 (APT)
                        z_val = z_val - 0.05 * exp(-((freq_ppm-3.5)/0.3)^2);
                    end
                    if abs(freq_ppm + 3.5) < 0.5  % rNOE效应
                        z_val = z_val + 0.03 * exp(-((freq_ppm+3.5)/0.4)^2);
                    end
                    if abs(freq_ppm - 2.0) < 0.3   % 胺效应
                        z_val = z_val - 0.02 * exp(-((freq_ppm-2.0)/0.2)^2);
                    end
                    
                    % 添加噪声
                    % Add noise
                    z_val = z_val + 0.01 * randn();
                    zSpec(i, j, 1, f) = max(0, min(1, z_val));
                end
            else
                mask(i, j, 1) = 0; % 背景区域
            end
        end
    end
    
    fprintf('   ✅ 已创建合成测试数据\n');
    fprintf('   ✅ Created synthetic test data\n');
end

%% 第3部分：准备推理数据 - Prepare Data for Inference
fprintf('\n>> 第3部分：准备推理数据\n');

data_size = size(zSpec);                        % 数据尺寸
n_freq = data_size(4);                          % 频率点数
param_maps = zeros([data_size(1:3), 15]);       % 15个参数图

% 将Z谱重塑为网络输入格式
% Reshape Z-spectra for network input
fprintf('   正在准备推理数据...\n');
fprintf('   Preparing data for inference...\n');

zSpec_reshaped = [];    % 重塑后的Z谱数据
valid_indices = [];     % 有效像素索引
counter = 0;            % 计数器

% 提取所有脑组织像素
% Extract all brain tissue voxels
for s = 1:data_size(3)
    for i = 1:data_size(1)
        for j = 1:data_size(2)
            if mask(i, j, s) == 1  % 脑组织像素
                counter = counter + 1;
                zSpec_reshaped(:, counter) = squeeze(zSpec(i, j, s, :));
                valid_indices(counter, :) = [i, j, s];  % 记录像素位置
            end
        end
    end
end

fprintf('   正在处理 %d 个有效像素...\n', counter);
fprintf('   Processing %d valid voxels...\n', counter);

%% 第4部分：执行网络推理 - Apply Network Inference
fprintf('\n>> 第4部分：执行网络推理\n');

fprintf('   正在运行网络推理...\n');
fprintf('   Running network inference...\n');

% 记录推理开始时间
% Record inference start time
tic;

% 使用训练好的网络进行预测
% Use trained network for prediction
param_outputs = net(zSpec_reshaped, 'useParallel', 'yes', 'useGPU', 'no');

% 计算推理时间
% Calculate inference time
inference_time = toc;
fprintf('   ✅ 推理完成，用时 %.2f 秒\n', inference_time);
fprintf('   ✅ Inference completed in %.2f seconds\n', inference_time);

%% 第5部分：将结果重塑回图像格式 - Reshape Results Back to Image Format
fprintf('\n>> 第5部分：将结果重塑回图像格式\n');

fprintf('   正在将结果重塑为图像格式...\n');
fprintf('   Reshaping results to image format...\n');

% 将预测结果填回到参数图中
% Fill prediction results back into parameter maps
for idx = 1:size(valid_indices, 1)
    i = valid_indices(idx, 1);  % 行索引
    j = valid_indices(idx, 2);  % 列索引
    s = valid_indices(idx, 3);  % 层索引
    param_maps(i, j, s, :) = param_outputs(:, idx);  % 填入15个参数
end

fprintf('   ✅ 结果重塑完成\n');
fprintf('   ✅ Result reshaping completed\n');

%% 第6部分：显示推理结果 - Display Inference Results
fprintf('\n=== 第6部分：显示推理结果 ===\n');
fprintf('=== Part 6: Display Inference Results ===\n');

% 参数名称（中英文对照）
% Parameter names (Chinese-English)
param_names_display = {
    '水池振幅/Water Amplitude', 'MTR振幅/MTR Amplitude', 'APT振幅/APT Amplitude', 'NOE振幅/NOE Amplitude', 'Cr振幅/Cr Amplitude', ...
    '水池积分/Water Integral', 'MTR积分/MTR Integral', 'APT积分/APT Integral', 'NOE积分/NOE Integral', 'Cr积分/Cr Integral', ...
    '水池位置/Water Position', 'MTR位置/MTR Position', 'APT位置/APT Position', 'NOE位置/NOE Position', 'Cr位置/Cr Position'
};

% 显示关键参数图
% Display key parameter maps
key_params = [2, 4, 7, 10, 13]; % B0偏移, 酰胺交换, 胺交换, rNOE交换, MT振幅
key_names = {'B0偏移/B0 Offset', '酰胺交换/Amide Exchange', '胺交换/Amine Exchange', 'rNOE交换/rNOE Exchange', 'MT振幅/MT Amplitude'};

% 颜色轴范围（根据您的数据调整）
% Color axis ranges (adjust based on your data)
caxis_ranges = [
    -50, 50;     % B0偏移
    0, 100;      % 酰胺交换
    0, 150;      % 胺交换  
    0, 50;       % rNOE交换
    0, 0.3;      % MT振幅
];

fprintf('   正在创建参数图可视化...\n');
fprintf('   Creating parameter map visualization...\n');

figure('Position', [100, 100, 1500, 600]);
for p = 1:length(key_params)
    param_idx = key_params(p);
    
    subplot(2, 3, p);
    % 应用脑掩膜的参数图
    % Parameter map with brain mask applied
    param_map = squeeze(param_maps(:, :, 1, param_idx)) .* mask(:, :, 1);
    imagesc(param_map);
    colorbar;
    caxis(caxis_ranges(p, :));
    title(sprintf('%s', key_names{p}));
    axis image off;
    colormap(gca, 'jet');
end

% 添加脑掩膜显示
% Add brain mask display
subplot(2, 3, 6);
imagesc(mask(:, :, 1));
colorbar;
title('脑掩膜/Brain Mask');
axis image off;
colormap(gca, 'gray');

sgtitle('5池DeepCEST参数图 / 5-Pool DeepCEST Parameter Maps');

fprintf('   ✅ 参数图可视化已创建\n');
fprintf('   ✅ Parameter map visualization created\n');

%% 第7部分：保存推理结果 - Save Inference Results
fprintf('\n=== 第7部分：保存推理结果 ===\n');
fprintf('=== Part 7: Save Inference Results ===\n');

% 创建Results文件夹（如果不存在）
% Create Results directory if it doesn't exist
if ~exist('Results', 'dir')
    mkdir('Results');
    fprintf('   已创建Results文件夹\n');
    fprintf('   Created Results folder\n');
end

% 保存结果文件
% Save results file
results_file = 'Results/DeepCEST_3T_to_7T_Results.mat';

% 保存推理结果和相关信息
% Save inference results and related information
save(results_file, 'param_maps', 'mask', 'param_names_display', 'key_params', 'key_names', ...
     'network_path', 'inference_time', 'data_size', 'zSpec', 'valid_indices');

fprintf('   ✅ 结果已保存到: %s\n', results_file);
fprintf('   ✅ Results saved to: %s\n', results_file);

%% 第8部分：显示统计摘要 - Display Summary Statistics
fprintf('\n=== 第8部分：显示统计摘要 ===\n');
fprintf('=== Part 8: Display Summary Statistics ===\n');

fprintf('参数图统计:\n');
fprintf('Parameter Map Statistics:\n');

for p = 1:length(key_params)
    param_idx = key_params(p);
    param_data = param_maps(:, :, :, param_idx);  % 参数数据
    brain_data = param_data(mask == 1);           % 脑组织数据

    fprintf('%s:\n', key_names{p});
    fprintf('  均值 ± 标准差: %.3f ± %.3f\n', mean(brain_data), std(brain_data));
    fprintf('  Mean ± Std: %.3f ± %.3f\n', mean(brain_data), std(brain_data));
    fprintf('  范围: [%.3f, %.3f]\n', min(brain_data), max(brain_data));
    fprintf('  Range: [%.3f, %.3f]\n', min(brain_data), max(brain_data));
    fprintf('  中位数: %.3f\n', median(brain_data));
    fprintf('  Median: %.3f\n', median(brain_data));
    fprintf('\n');
end

%% 第9部分：绘制样本Z谱和拟合 - Plot Sample Z-spectra and Fits
fprintf('\n=== 第9部分：绘制样本Z谱和拟合 ===\n');
fprintf('=== Part 9: Plot Sample Z-spectra and Fits ===\n');

if exist('ppm_offsets', 'var')
    fprintf('   正在创建Z谱样本图...\n');
    fprintf('   Creating Z-spectra sample plots...\n');

    figure('Position', [100, 100, 1200, 400]);

    % 选择几个代表性像素
    % Select a few representative voxels
    n_samples = min(5, size(zSpec_reshaped, 2));
    sample_indices = round(linspace(1, size(zSpec_reshaped, 2), n_samples));

    % 绘制样本Z谱
    % Plot sample Z-spectra
    subplot(1, 2, 1);
    plot(ppm_offsets, zSpec_reshaped(:, sample_indices), 'o-', 'LineWidth', 1.5);
    xlabel('频率偏移 (ppm) / Frequency Offset (ppm)');
    ylabel('Z谱强度 / Z-spectrum Intensity');
    title('样本Z谱 / Sample Z-spectra');
    grid on;
    legend(arrayfun(@(x) sprintf('像素 %d / Voxel %d', x), sample_indices, 'UniformOutput', false));

    % 显示这些样本的参数值
    % Show parameter values for these samples
    subplot(1, 2, 2);
    param_subset = param_outputs(key_params, sample_indices);
    imagesc(param_subset);
    colorbar;
    xlabel('样本像素 / Sample Voxels');
    ylabel('参数 / Parameters');
    title('参数值 / Parameter Values');
    set(gca, 'YTick', 1:length(key_params), 'YTickLabel', key_names);

    fprintf('   ✅ Z谱样本图已创建\n');
    fprintf('   ✅ Z-spectra sample plots created\n');
end

%% 第10部分：推理性能评估 - Inference Performance Assessment
fprintf('\n=== 第10部分：推理性能评估 ===\n');
fprintf('=== Part 10: Inference Performance Assessment ===\n');

% 计算推理效率
% Calculate inference efficiency
voxels_per_second = counter / inference_time;
total_params_computed = counter * 15;

fprintf('推理性能:\n');
fprintf('Inference Performance:\n');
fprintf('  处理像素数: %d\n', counter);
fprintf('  Processed voxels: %d\n', counter);
fprintf('  推理用时: %.2f 秒\n', inference_time);
fprintf('  Inference time: %.2f seconds\n', inference_time);
fprintf('  处理速度: %.1f 像素/秒\n', voxels_per_second);
fprintf('  Processing speed: %.1f voxels/second\n', voxels_per_second);
fprintf('  计算参数总数: %d\n', total_params_computed);
fprintf('  Total parameters computed: %d\n', total_params_computed);

% 与传统方法的速度比较估算
% Speed comparison estimate with traditional methods
traditional_time_estimate = counter * 0.5; % 假设传统方法每像素需要0.5秒
speedup_factor = traditional_time_estimate / inference_time;

fprintf('\n与传统方法比较:\n');
fprintf('Comparison with traditional methods:\n');
fprintf('  估计传统方法用时: %.1f 秒 (%.1f 分钟)\n', traditional_time_estimate, traditional_time_estimate/60);
fprintf('  Estimated traditional method time: %.1f seconds (%.1f minutes)\n', traditional_time_estimate, traditional_time_estimate/60);
fprintf('  加速倍数: %.1fx\n', speedup_factor);
fprintf('  Speedup factor: %.1fx\n', speedup_factor);

%% 第11部分：质量检查建议 - Quality Check Recommendations
fprintf('\n=== 第11部分：质量检查建议 ===\n');
fprintf('=== Part 11: Quality Check Recommendations ===\n');

fprintf('质量检查建议:\n');
fprintf('Quality Check Recommendations:\n');

% 检查参数范围是否合理
% Check if parameter ranges are reasonable
reasonable_ranges = true;
for p = 1:length(key_params)
    param_idx = key_params(p);
    param_data = param_maps(:, :, :, param_idx);
    brain_data = param_data(mask == 1);

    % 简单的合理性检查
    % Simple reasonableness check
    if param_idx == 2 && (min(brain_data) < -100 || max(brain_data) > 100)  % B0偏移
        fprintf('  ⚠️  B0偏移范围可能不合理: [%.1f, %.1f] Hz\n', min(brain_data), max(brain_data));
        reasonable_ranges = false;
    elseif param_idx == 4 && (min(brain_data) < 0 || max(brain_data) > 200)  % 酰胺交换
        fprintf('  ⚠️  酰胺交换率范围可能不合理: [%.1f, %.1f] s⁻¹\n', min(brain_data), max(brain_data));
        reasonable_ranges = false;
    end
end

if reasonable_ranges
    fprintf('  ✅ 参数范围看起来合理\n');
    fprintf('  ✅ Parameter ranges appear reasonable\n');
end

fprintf('\n建议的后续步骤:\n');
fprintf('Recommended follow-up steps:\n');
fprintf('1. 检查参数图的空间分布是否符合解剖学预期\n');
fprintf('1. Check if parameter map spatial distributions match anatomical expectations\n');
fprintf('2. 与传统拟合方法的结果进行比较验证\n');
fprintf('2. Compare with traditional fitting method results for validation\n');
fprintf('3. 在已知参数的模拟数据上测试准确性\n');
fprintf('3. Test accuracy on phantom data with known parameters\n');
fprintf('4. 评估不同组织类型的参数差异\n');
fprintf('4. Evaluate parameter differences across tissue types\n');

%% 第12部分：完成总结 - Completion Summary
fprintf('\n=== 第12部分：完成总结 ===\n');
fprintf('=== Part 12: Completion Summary ===\n');

fprintf('推理任务完成总结:\n');
fprintf('Inference Task Completion Summary:\n');
fprintf('  ✅ 成功加载训练好的5池DeepCEST网络\n');
fprintf('  ✅ Successfully loaded trained 5-pool DeepCEST network\n');
fprintf('  ✅ 处理了 %d 个脑组织像素\n', counter);
fprintf('  ✅ Processed %d brain tissue voxels\n', counter);
fprintf('  ✅ 生成了 15 个参数图\n');
fprintf('  ✅ Generated 15 parameter maps\n');
fprintf('  ✅ 推理速度: %.1f 像素/秒\n', voxels_per_second);
fprintf('  ✅ Inference speed: %.1f voxels/second\n', voxels_per_second);
fprintf('  ✅ 结果已保存到 Results 文件夹\n');
fprintf('  ✅ Results saved to Results folder\n');

fprintf('\n输出文件:\n');
fprintf('Output files:\n');
fprintf('  - 参数图: %s\n', results_file);
fprintf('  - Parameter maps: %s\n', results_file);
fprintf('  - 可视化图表: 当前MATLAB图形窗口\n');
fprintf('  - Visualization plots: Current MATLAB figure windows\n');

%% 第13部分：生成DCM参数图文件 - Generate DCM Parameter Map Files
fprintf('\n=== 第13部分：生成DCM参数图文件 ===\n');
fprintf('=== Part 13: Generate DCM Parameter Map Files ===\n');

% 创建DCM输出文件夹
% Create DCM output folder
dcm_output_dir = 'Results/DCM_ParameterMaps';
if ~exist(dcm_output_dir, 'dir')
    mkdir(dcm_output_dir);
    fprintf('   已创建DCM输出文件夹: %s\n', dcm_output_dir);
    fprintf('   Created DCM output folder: %s\n', dcm_output_dir);
end

% 15个参数的DCM文件名（与拟合脚本保持一致的命名）
% DCM filenames for 15 parameters (consistent with fitting script naming)
dcm_filenames = {
    'WaterAmplitude.dcm', 'MTRAmplitude.dcm', 'APTAmplitude.dcm', 'NOEAmplitude.dcm', 'CrAmplitude.dcm', ...
    'WaterIntegral.dcm', 'MTRIntegral.dcm', 'APTIntegral.dcm', 'NOEIntegral.dcm', 'CrIntegral.dcm', ...
    'WaterPosition.dcm', 'MTRPosition.dcm', 'APTPosition.dcm', 'NOEPosition.dcm', 'CrPosition.dcm'
};

% 参数描述（用于DCM元数据）
% Parameter descriptions (for DCM metadata)
param_descriptions = {
    '7T Water Pool Amplitude', '7T MTR Pool Amplitude', '7T APT Pool Amplitude', '7T NOE Pool Amplitude', '7T Cr Pool Amplitude', ...
    '7T Water Pool Integral', '7T MTR Pool Integral', '7T APT Pool Integral', '7T NOE Pool Integral', '7T Cr Pool Integral', ...
    '7T Water Pool Position', '7T MTR Pool Position', '7T APT Pool Position', '7T NOE Pool Position', '7T Cr Pool Position'
};

fprintf('   正在生成15个参数的DCM文件...\n');
fprintf('   Generating DCM files for 15 parameters...\n');

% 逐个生成DCM文件
% Generate DCM files one by one
for param_idx = 1:15
    % 获取当前参数图
    % Get current parameter map
    current_param_map = squeeze(param_maps(:, :, 1, param_idx));

    % 应用脑掩膜（背景设为0）
    % Apply brain mask (set background to 0)
    current_param_map = current_param_map .* mask(:, :, 1);

    % DCM文件路径
    % DCM file path
    dcm_filepath = fullfile(dcm_output_dir, dcm_filenames{param_idx});

    % 生成DCM文件
    % Generate DCM file
    try
        % 创建DICOM信息结构
        % Create DICOM info structure
        info = struct();
        info.Filename = dcm_filepath;
        info.FileModDate = datestr(now);
        info.FileSize = [];
        info.Format = 'DICOM';
        info.FormatVersion = 3;
        info.Width = size(current_param_map, 2);
        info.Height = size(current_param_map, 1);
        info.BitDepth = 32;
        info.ColorType = 'grayscale';

        % DICOM特定信息
        % DICOM specific information
        info.StudyDescription = '3T to 7T DeepCEST Parameter Maps';
        info.SeriesDescription = param_descriptions{param_idx};
        info.ImageType = 'DERIVED\SECONDARY\OTHER';
        info.Modality = 'MR';
        info.StudyDate = datestr(now, 'yyyymmdd');
        info.SeriesDate = datestr(now, 'yyyymmdd');
        info.StudyTime = datestr(now, 'HHMMSS');
        info.SeriesTime = datestr(now, 'HHMMSS');
        info.StudyID = 'DeepCEST_3T_to_7T';
        info.SeriesNumber = param_idx;
        info.InstanceNumber = 1;
        info.SliceThickness = 5.0;  % 默认层厚
        info.PixelSpacing = [1.0; 1.0];  % 默认像素间距

        % 数据类型转换（确保为单精度浮点）
        % Data type conversion (ensure single precision float)
        param_data_single = single(current_param_map);

        % 写入DCM文件
        % Write DCM file
        dicomwrite(param_data_single, dcm_filepath, info);

        fprintf('   ✅ 已生成: %s\n', dcm_filenames{param_idx});

    catch ME
        fprintf('   ❌ 生成 %s 失败: %s\n', dcm_filenames{param_idx}, ME.message);
        fprintf('   ❌ Failed to generate %s: %s\n', dcm_filenames{param_idx}, ME.message);
    end
end

% 生成汇总信息文件
% Generate summary information file
summary_file = fullfile(dcm_output_dir, 'ParameterMap_Summary.txt');
fid = fopen(summary_file, 'w');
if fid ~= -1
    fprintf(fid, '3T→7T跨场强DeepCEST参数图汇总\n');
    fprintf(fid, '3T→7T Cross-Field DeepCEST Parameter Map Summary\n');
    fprintf(fid, '生成时间 / Generated: %s\n\n', datestr(now));

    fprintf(fid, '参数文件列表 / Parameter File List:\n');
    for param_idx = 1:15
        fprintf(fid, '%2d. %s - %s\n', param_idx, dcm_filenames{param_idx}, param_descriptions{param_idx});
    end

    fprintf(fid, '\n数据信息 / Data Information:\n');
    fprintf(fid, '图像尺寸 / Image Size: %d × %d\n', size(param_maps, 1), size(param_maps, 2));
    fprintf(fid, '处理像素数 / Processed Voxels: %d\n', counter);
    fprintf(fid, '推理用时 / Inference Time: %.2f 秒\n', inference_time);
    fprintf(fid, '网络文件 / Network File: %s\n', network_files(idx).name);

    fclose(fid);
    fprintf('   ✅ 已生成汇总信息: ParameterMap_Summary.txt\n');
    fprintf('   ✅ Generated summary info: ParameterMap_Summary.txt\n');
end

fprintf('\n   📁 DCM文件输出位置: %s\n', dcm_output_dir);
fprintf('   📁 DCM files output location: %s\n', dcm_output_dir);
fprintf('   📊 共生成 %d 个DCM参数图文件\n', 15);
fprintf('   📊 Generated %d DCM parameter map files\n', 15);

fprintf('\n=== 步骤4完成 ===\n');
fprintf('=== Step 4 Complete ===\n');
fprintf('3T→7T跨场强DeepCEST模型推理测试完成！\n');
fprintf('3T→7T Cross-Field DeepCEST model inference testing completed!\n');
fprintf('✅ DCM参数图文件已生成，可用于医学影像查看软件\n');
fprintf('✅ DCM parameter map files generated, ready for medical imaging viewers\n');

%% ========================================================================
%% 内置函数：数据格式检测和转换 - Built-in Function: Data Format Detection and Conversion
%% ========================================================================

function [zSpec_4D, mask_4D, ppm_offsets, data_format] = detect_and_convert_data_format_internal()
% 检测数据格式并转换为4D格式用于推理显示
% Detect data format and convert to 4D format for inference display

fprintf('   >> 检测数据格式...\n');
fprintf('   >> Detecting data format...\n');

% 检查工作空间中的变量
% Check variables in workspace
vars = evalin('caller', 'who');

if ismember('zInput', vars) && ismember('pTarget', vars)
    %% 检测到2D向量格式 (来自拟合脚本)
    fprintf('   ✅ 检测到2D向量格式数据 (拟合脚本输出)\n');
    fprintf('   ✅ Detected 2D vector format data (fitting script output)\n');

    % 获取2D数据
    zInput = evalin('caller', 'zInput');
    pTarget = evalin('caller', 'pTarget');

    % 获取频率偏移
    if ismember('offs', vars)
        ppm_offsets = evalin('caller', 'offs');
    elseif ismember('ppm_offsets', vars)
        ppm_offsets = evalin('caller', 'pmp_offsets');
    else
        % 生成默认频率偏移
        n_freq = size(zInput, 1);
        ppm_offsets = linspace(-6, 6, n_freq)';
        fprintf('   ⚠️  未找到频率偏移，使用默认值\n');
    end

    fprintf('   数据尺寸: zInput=%s, pTarget=%s\n', mat2str(size(zInput)), mat2str(size(pTarget)));
    fprintf('   Data dimensions: zInput=%s, pTarget=%s\n', mat2str(size(zInput)), mat2str(size(pTarget)));

    % 转换为4D格式
    [zSpec_4D, mask_4D] = convert_2D_to_4D_format(zInput, pTarget, ppm_offsets);
    data_format = '2D_converted';

elseif ismember('zSpec', vars) && ismember('mask', vars)
    %% 检测到4D图像格式 (标准格式)
    fprintf('   ✅ 检测到4D图像格式数据 (标准格式)\n');
    fprintf('   ✅ Detected 4D image format data (standard format)\n');

    % 直接使用4D数据
    zSpec_4D = evalin('caller', 'zSpec');
    mask_4D = evalin('caller', 'mask');

    % 获取频率偏移
    if ismember('offs', vars)
        pmp_offsets = evalin('caller', 'offs');
    elseif ismember('ppm_offsets', vars)
        pmp_offsets = evalin('caller', 'pmp_offsets');
    else
        n_freq = size(zSpec_4D, 4);
        pmp_offsets = linspace(-6, 6, n_freq)';
        fprintf('   ⚠️  未找到频率偏移，使用默认值\n');
    end

    fprintf('   数据尺寸: zSpec=%s, mask=%s\n', mat2str(size(zSpec_4D)), mat2str(size(mask_4D)));
    fprintf('   Data dimensions: zSpec=%s, mask=%s\n', mat2str(size(zSpec_4D)), mat2str(size(mask_4D)));
    data_format = '4D_standard';

else
    error('❌ 无法识别数据格式。需要 (zInput+pTarget) 或 (zSpec+mask)\nCannot recognize data format. Need (zInput+pTarget) or (zSpec+mask)');
end

fprintf('   ✅ 数据格式转换完成: %s\n', data_format);
fprintf('   ✅ Data format conversion completed: %s\n', data_format);

end

function [zSpec_4D, mask_4D] = convert_2D_to_4D_format(zInput, pTarget, pmp_offsets)
% 将2D向量格式转换为4D图像格式
% Convert 2D vector format to 4D image format

fprintf('   >> 执行2D→4D格式转换...\n');
fprintf('   >> Performing 2D→4D format conversion...\n');

[n_freq, n_voxels] = size(zInput);
fprintf('   输入数据: %d个频率点, %d个像素\n', n_freq, n_voxels);
fprintf('   Input data: %d frequency points, %d voxels\n', n_freq, n_voxels);

% 估算图像尺寸 (假设为方形或接近方形)
% Estimate image dimensions (assume square or near-square)
img_size = ceil(sqrt(n_voxels));
total_pixels = img_size * img_size;

fprintf('   估算图像尺寸: %d×%d (总像素: %d)\n', img_size, img_size, total_pixels);
fprintf('   Estimated image size: %d×%d (total pixels: %d)\n', img_size, img_size, total_pixels);

% 初始化4D数据
zSpec_4D = zeros(img_size, img_size, 1, n_freq);
mask_4D = zeros(img_size, img_size, 1);

% 将2D数据重塑为4D格式
% Reshape 2D data to 4D format
for i = 1:n_voxels
    % 计算在4D数组中的位置
    [row, col] = ind2sub([img_size, img_size], i);

    % 填入Z谱数据
    zSpec_4D(row, col, 1, :) = zInput(:, i);

    % 设置掩膜 (所有有数据的像素都标记为脑组织)
    mask_4D(row, col, 1) = 1;
end

% 如果有剩余的像素位置，保持为0 (背景)
fprintf('   有效像素: %d/%d (%.1f%%)\n', n_voxels, total_pixels, 100*n_voxels/total_pixels);
fprintf('   Valid pixels: %d/%d (%.1f%%)\n', n_voxels, total_pixels, 100*n_voxels/total_pixels);

% 数据质量检查
z_min = min(zSpec_4D(:));
z_max = max(zSpec_4D(:));
fprintf('   Z谱数据范围: [%.4f, %.4f]\n', z_min, z_max);
fprintf('   Z-spectrum data range: [%.4f, %.4f]\n', z_min, z_max);

fprintf('   ✅ 2D→4D转换完成\n');
fprintf('   ✅ 2D→4D conversion completed\n');

end

function [zSpec, mask, pmp_offsets] = generate_synthetic_cest_data_internal(offs, pmp_offsets)
% 生成合成CEST测试数据 (内部函数)
% Generate synthetic CEST test data (internal function)

fprintf('   >> 生成合成CEST测试数据...\n');
fprintf('   >> Generating synthetic CEST test data...\n');

% 图像参数
[height, width, slices] = deal(64, 64, 1);
n_freq = length(offs);

fprintf('   图像尺寸: %dx%dx%dx%d\n', height, width, slices, n_freq);
fprintf('   Image dimensions: %dx%dx%dx%d\n', height, width, slices, n_freq);

% 初始化数据
zSpec = zeros(height, width, slices, n_freq);
mask = zeros(height, width, slices);

% 生成模拟脑组织数据
for i = 1:height
    for j = 1:width
        if sqrt((i-32)^2 + (j-32)^2) < 25 % 脑区域
            % 模拟真实的Z谱
            for f = 1:n_freq
                freq_ppm = pmp_offsets(f);
                z_val = 1.0;  % 基线值

                % 添加CEST效应
                if abs(freq_ppm - 3.5) < 0.5  % 酰胺效应 (APT)
                    z_val = z_val - 0.05 * exp(-((freq_ppm-3.5)/0.3)^2);
                end
                if abs(freq_ppm + 3.5) < 0.5  % rNOE效应
                    z_val = z_val + 0.03 * exp(-((freq_ppm+3.5)/0.4)^2);
                end
                if abs(freq_ppm - 2.0) < 0.3   % 胺效应
                    z_val = z_val - 0.02 * exp(-((freq_ppm-2.0)/0.2)^2);
                end

                % 添加噪声
                z_val = z_val + 0.01 * randn();
                zSpec(i, j, 1, f) = max(0, min(1, z_val));
            end
            mask(i, j, 1) = 1; % 脑组织
        else
            mask(i, j, 1) = 0; % 背景区域
        end
    end
end

fprintf('   ✅ 合成数据生成完成\n');
fprintf('   ✅ Synthetic data generation completed\n');

end
